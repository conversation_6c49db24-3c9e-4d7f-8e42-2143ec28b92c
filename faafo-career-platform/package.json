{"name": "faafo-career-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:auth": "jest __tests__/core-flows/authentication.test.ts", "test:assessment": "jest __tests__/core-flows/assessment.test.ts", "test:resources": "jest __tests__/core-flows/learning-resources.test.ts", "test:forum": "jest __tests__/core-flows/forum.test.ts", "test:api": "jest __tests__/api/endpoints.test.ts", "test:ui": "jest __tests__/components/ui-components.test.tsx", "test:security": "jest __tests__/security/security.test.ts", "test:performance": "jest __tests__/performance/performance.test.ts", "test:comprehensive": "tsx __tests__/run-comprehensive-tests.ts", "test-crud": "tsx scripts/test-prisma-crud.ts", "prisma:seed": "tsx prisma/seed.ts", "postinstall": "prisma generate"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@hookform/resolvers": "^5.1.0", "@prisma/client": "^6.8.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/components": "^0.0.41", "@react-email/html": "^0.0.11", "@sentry/nextjs": "^9.27.0", "@types/node-fetch": "^2.6.12", "@vercel/analytics": "^1.5.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "fuse.js": "^7.1.0", "lucide-react": "^0.511.0", "next": "15.3.3", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "react": "^19.1.0", "react-cookie-consent": "^9.0.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "resend": "^4.5.1", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "zod": "^3.25.56"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.52.0", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcrypt": "^5.0.2", "@types/eslint-plugin-security": "^3.0.0", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/testing-library__jest-dom": "^5.14.9", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "dotenv": "^16.5.0", "eslint": "^9.28.0", "eslint-config-next": "15.3.3", "eslint-plugin-security": "^3.0.1", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "prettier": "^3.5.3", "prisma": "^6.8.2", "tailwindcss": "^4", "tailwindcss-animate": "^1.0.7", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.4", "tw-animate-css": "^1.3.2", "typescript": "^5"}}